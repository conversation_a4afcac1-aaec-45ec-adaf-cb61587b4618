// Test script to verify extension structure and basic functionality
// Run this in browser console after loading the extension

console.log('🧪 Testing Prompt Processor Extension...');

// Test 1: Check if extension files are accessible
function testExtensionStructure() {
    console.log('📁 Testing extension structure...');
    
    const requiredFiles = [
        'manifest.json',
        'popup.html',
        'popup.css', 
        'popup.js',
        'content.js',
        'background.js'
    ];
    
    console.log('✅ Required files should be present:', requiredFiles);
    return true;
}

// Test 2: Check if content script is loaded
function testContentScript() {
    console.log('📜 Testing content script...');
    
    // Check if our content script classes are available
    if (typeof WebpageInteractor !== 'undefined') {
        console.log('✅ Content script loaded successfully');
        return true;
    } else {
        console.log('❌ Content script not found');
        return false;
    }
}

// Test 3: Test textarea detection
function testTextareaDetection() {
    console.log('🔍 Testing textarea detection...');

    const textareas = document.querySelectorAll('textarea');
    console.log(`Found ${textareas.length} textarea(s) on page`);

    if (textareas.length > 0) {
        textareas.forEach((textarea, index) => {
            const rect = textarea.getBoundingClientRect();
            const isVisible = rect.width > 0 && rect.height > 0;
            console.log(`Textarea ${index + 1}:`, {
                placeholder: textarea.placeholder,
                classes: textarea.className,
                visible: isVisible,
                disabled: textarea.disabled,
                readonly: textarea.readOnly,
                value: textarea.value
            });
        });

        // Test specific Runware selector
        const runwareTextarea = document.querySelector('textarea[placeholder="Type your prompt here to start..."]');
        if (runwareTextarea) {
            console.log('✅ Found Runware-specific textarea:', runwareTextarea);
        }

        return true;
    } else {
        console.log('❌ No textareas found on page');
        return false;
    }
}

// Test 4: Test button detection
function testButtonDetection() {
    console.log('🔘 Testing button detection...');

    const buttons = document.querySelectorAll('button, input[type="submit"]');
    console.log(`Found ${buttons.length} button(s) on page`);

    const generateButtons = Array.from(buttons).filter(button => {
        const text = button.textContent?.toLowerCase() || button.value?.toLowerCase() || '';
        const id = button.id?.toLowerCase() || '';
        return text.includes('generate') || text.includes('send') || text.includes('submit') || id.includes('submit');
    });

    console.log(`Found ${generateButtons.length} potential generate button(s)`);

    generateButtons.forEach((button, index) => {
        console.log(`Generate button ${index + 1}:`, {
            text: button.textContent || button.value,
            id: button.id,
            classes: button.className,
            disabled: button.disabled,
            hasDisabledAttr: button.hasAttribute('disabled'),
            visible: button.getBoundingClientRect().width > 0
        });
    });

    // Test specific Runware button selector
    const runwareButton = document.querySelector('button[id*="submit-btn"]');
    if (runwareButton) {
        console.log('✅ Found Runware-specific button:', {
            element: runwareButton,
            text: runwareButton.textContent,
            disabled: runwareButton.disabled,
            id: runwareButton.id
        });
    }

    return generateButtons.length > 0;
}

// Test 5: Test extension messaging (if available)
function testExtensionMessaging() {
    console.log('📨 Testing extension messaging...');
    
    if (typeof browser !== 'undefined' && browser.runtime) {
        console.log('✅ Browser extension API available');
        
        // Try to send a test message
        try {
            browser.runtime.sendMessage({
                type: 'test',
                message: 'Testing extension communication'
            }).then(() => {
                console.log('✅ Extension messaging works');
            }).catch(error => {
                console.log('⚠️ Extension messaging error:', error.message);
            });
        } catch (error) {
            console.log('⚠️ Could not test messaging:', error.message);
        }
        
        return true;
    } else {
        console.log('❌ Browser extension API not available');
        return false;
    }
}

// Test 6: Check page compatibility
function testPageCompatibility() {
    console.log('🌐 Testing page compatibility...');
    
    const url = window.location.href;
    console.log('Current URL:', url);
    
    const compatiblePatterns = [
        /runware\.ai/i,
        /playground/i,
        /openai\.com/i,
        /claude\.ai/i
    ];
    
    const isCompatible = compatiblePatterns.some(pattern => pattern.test(url));
    
    if (isCompatible) {
        console.log('✅ Page appears to be compatible');
    } else {
        console.log('⚠️ Page may not be fully compatible');
    }
    
    return isCompatible;
}

// Run all tests
function runAllTests() {
    console.log('🚀 Starting extension tests...\n');
    
    const tests = [
        { name: 'Extension Structure', fn: testExtensionStructure },
        { name: 'Content Script', fn: testContentScript },
        { name: 'Textarea Detection', fn: testTextareaDetection },
        { name: 'Button Detection', fn: testButtonDetection },
        { name: 'Extension Messaging', fn: testExtensionMessaging },
        { name: 'Page Compatibility', fn: testPageCompatibility }
    ];
    
    const results = tests.map(test => {
        console.log(`\n--- ${test.name} ---`);
        const result = test.fn();
        console.log(`Result: ${result ? '✅ PASS' : '❌ FAIL'}`);
        return { name: test.name, passed: result };
    });
    
    console.log('\n📊 Test Summary:');
    results.forEach(result => {
        console.log(`${result.passed ? '✅' : '❌'} ${result.name}`);
    });
    
    const passedCount = results.filter(r => r.passed).length;
    console.log(`\n🎯 ${passedCount}/${results.length} tests passed`);
    
    if (passedCount === results.length) {
        console.log('🎉 All tests passed! Extension should work correctly.');
    } else {
        console.log('⚠️ Some tests failed. Check the issues above.');
    }
}

// Auto-run tests when script is loaded
runAllTests();

// Export functions for manual testing
window.extensionTests = {
    runAllTests,
    testExtensionStructure,
    testContentScript,
    testTextareaDetection,
    testButtonDetection,
    testExtensionMessaging,
    testPageCompatibility
};
