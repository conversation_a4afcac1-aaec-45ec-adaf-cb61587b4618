# Prompt Processor Firefox Extension

A Firefox extension that automatically processes and submits prompts to AI interfaces like Runware.ai, OpenAI Playground, and other AI platforms.

## Features

- **Batch Prompt Processing**: Add multiple prompts and process them automatically one by one
- **Smart Detection**: Automatically finds textarea and generate buttons on AI websites
- **Customizable Delays**: Set delays between prompt submissions
- **Auto-scroll**: Automatically scrolls to the textarea for better visibility
- **Progress Tracking**: Visual progress bar and status indicators
- **Pause/Resume**: Control processing with pause and resume functionality
- **Persistent Storage**: Saves your prompts and settings between sessions

## Installation

1. Open Firefox and navigate to `about:debugging`
2. Click "This Firefox" in the left sidebar
3. Click "Load Temporary Add-on"
4. Navigate to the extension folder and select `manifest.json`
5. The extension will be loaded and the icon will appear in the toolbar

## Usage

1. **Navigate to a supported AI website** (e.g., runware.ai, OpenAI Playground)
2. **Click the extension icon** in the Firefox toolbar
3. **Add your prompts** in the text area:
   - Enter one prompt per line, or
   - Separate multiple prompts with `---`
   - Use Ctrl+Enter to quickly add prompts
4. **Configure settings** (optional):
   - Set delay between prompts (1-60 seconds)
   - Enable/disable auto-scroll to textarea
5. **Click "Start Processing"** to begin automatic prompt submission
6. **Monitor progress** through the visual progress bar and status indicators

## Supported Websites

The extension works on websites that contain:
- Text areas for prompt input
- Generate/Submit buttons
- Common AI interfaces

Specifically tested on:
- Runware.ai
- OpenAI Playground
- Claude.ai
- And other similar AI platforms

## Controls

- **Start Processing**: Begin processing the prompt queue
- **Pause**: Temporarily pause processing (can be resumed)
- **Stop**: Stop processing and reset to beginning
- **Clear All**: Remove all prompts from the queue

## Prompt Management

- **Add prompts**: Type in the input area and click "Add Prompts"
- **Remove individual prompts**: Click the × button next to any prompt
- **View status**: Each prompt shows its current status (pending, processing, completed, error)

## Settings

- **Delay between prompts**: Configurable delay (1-60 seconds) between each prompt submission
- **Auto-scroll**: Automatically scroll to the textarea when processing prompts

## Keyboard Shortcuts

- **Ctrl+Enter**: Quick add prompts from the input textarea

## Troubleshooting

### Extension not working on a website?
- Make sure the website has a visible textarea and generate button
- Check that the website is not blocking the extension
- Try refreshing the page and reopening the extension

### Prompts not being submitted?
- Verify the website's textarea and button are accessible
- Check if there are any error messages in the extension popup
- Try increasing the delay between prompts

### Generate button not found?
- The extension looks for buttons with text like "Generate", "Send", "Submit"
- Make sure such a button is visible on the page
- Try clicking the button manually first to ensure it works

## Development

### File Structure
```
├── manifest.json          # Extension manifest
├── popup.html             # Extension popup interface
├── popup.css              # Popup styling
├── popup.js               # Popup functionality
├── content.js             # Content script for webpage interaction
├── background.js          # Background script for coordination
├── icons/                 # Extension icons
└── README.md              # This file
```

### Key Components

- **Popup Interface**: User interface for managing prompts and settings
- **Content Script**: Interacts with the webpage to find elements and submit prompts
- **Background Script**: Coordinates communication between popup and content script

## Privacy

This extension:
- Only runs on pages you explicitly visit
- Does not collect or transmit any personal data
- Stores prompts and settings locally in your browser
- Does not communicate with external servers

## License

This project is open source. Feel free to modify and distribute according to your needs.

## Support

If you encounter any issues or have suggestions for improvements, please check the troubleshooting section above or modify the extension code to suit your specific needs.
