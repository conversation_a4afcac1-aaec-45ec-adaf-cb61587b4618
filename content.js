class WebpageInteractor {
    constructor() {
        this.isProcessing = false;
        this.selectors = {
            // Common selectors for AI interfaces
            textarea: [
                'textarea[placeholder*="prompt"]',
                'textarea[placeholder*="Type your prompt"]',
                'textarea[placeholder*="Enter your"]',
                'textarea.w-full',
                'textarea[name="prompt"]',
                'textarea[id*="prompt"]',
                '#promptInput',
                'textarea'
            ],
            generateButton: [
                'button:contains("Generate")',
                'button[type="submit"]',
                'button:contains("Send")',
                'button:contains("Submit")',
                'input[type="submit"]',
                'button.btn-primary',
                'button.generate-btn',
                '[data-testid="send-button"]'
            ]
        };
        
        this.init();
    }
    
    init() {
        // Listen for messages from popup
        browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.type === 'processPrompt') {
                this.processPrompt(message.prompt, message.autoScroll)
                    .then(result => sendResponse(result))
                    .catch(error => sendResponse({ success: false, error: error.message }));
                return true; // Keep message channel open for async response
            }
        });
        
        console.log('Prompt Processor content script loaded');
    }
    
    async processPrompt(promptText, autoScroll = true) {
        try {
            console.log('Processing prompt:', promptText);
            
            // Find textarea
            const textarea = this.findTextarea();
            if (!textarea) {
                throw new Error('Could not find textarea on this page');
            }
            
            // Scroll to textarea if enabled
            if (autoScroll) {
                textarea.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await this.sleep(500);
            }
            
            // Clear existing content and paste new prompt
            await this.pastePrompt(textarea, promptText);
            
            // Find and click generate button
            const generateButton = this.findGenerateButton();
            if (!generateButton) {
                throw new Error('Could not find generate button on this page');
            }
            
            // Click the generate button
            await this.clickGenerateButton(generateButton);
            
            // Send success message back to popup
            browser.runtime.sendMessage({
                type: 'promptProcessed',
                success: true
            });
            
            return { success: true };
            
        } catch (error) {
            console.error('Error processing prompt:', error);
            
            // Send error message back to popup
            browser.runtime.sendMessage({
                type: 'promptProcessed',
                success: false,
                error: error.message
            });
            
            return { success: false, error: error.message };
        }
    }
    
    findTextarea() {
        // Try each selector until we find a textarea
        for (const selector of this.selectors.textarea) {
            const elements = document.querySelectorAll(selector);
            for (const element of elements) {
                if (this.isValidTextarea(element)) {
                    console.log('Found textarea with selector:', selector);
                    return element;
                }
            }
        }
        
        // Fallback: find any visible textarea
        const allTextareas = document.querySelectorAll('textarea');
        for (const textarea of allTextareas) {
            if (this.isValidTextarea(textarea)) {
                console.log('Found textarea via fallback');
                return textarea;
            }
        }
        
        return null;
    }
    
    isValidTextarea(element) {
        if (!element || element.tagName !== 'TEXTAREA') return false;
        
        const style = window.getComputedStyle(element);
        const rect = element.getBoundingClientRect();
        
        // Check if element is visible and has reasonable size
        return style.display !== 'none' && 
               style.visibility !== 'hidden' && 
               rect.width > 50 && 
               rect.height > 20 &&
               !element.disabled &&
               !element.readOnly;
    }
    
    findGenerateButton() {
        // Try text-based search first
        const buttons = document.querySelectorAll('button, input[type="submit"], input[type="button"]');
        
        for (const button of buttons) {
            const text = button.textContent?.toLowerCase() || button.value?.toLowerCase() || '';
            const ariaLabel = button.getAttribute('aria-label')?.toLowerCase() || '';
            
            if (text.includes('generate') || 
                text.includes('send') || 
                text.includes('submit') ||
                ariaLabel.includes('generate') ||
                ariaLabel.includes('send')) {
                
                if (this.isValidButton(button)) {
                    console.log('Found generate button:', button);
                    return button;
                }
            }
        }
        
        // Try selector-based search
        for (const selector of this.selectors.generateButton) {
            try {
                const element = document.querySelector(selector);
                if (element && this.isValidButton(element)) {
                    console.log('Found generate button with selector:', selector);
                    return element;
                }
            } catch (e) {
                // Ignore invalid selectors
            }
        }
        
        // Fallback: look for primary buttons near textarea
        const textarea = this.findTextarea();
        if (textarea) {
            const nearbyButtons = this.findNearbyButtons(textarea);
            if (nearbyButtons.length > 0) {
                console.log('Found nearby button as fallback');
                return nearbyButtons[0];
            }
        }
        
        return null;
    }
    
    isValidButton(element) {
        if (!element) return false;
        
        const style = window.getComputedStyle(element);
        const rect = element.getBoundingClientRect();
        
        return style.display !== 'none' && 
               style.visibility !== 'hidden' && 
               rect.width > 10 && 
               rect.height > 10 &&
               !element.disabled;
    }
    
    findNearbyButtons(textarea) {
        const textareaRect = textarea.getBoundingClientRect();
        const buttons = document.querySelectorAll('button, input[type="submit"]');
        const nearbyButtons = [];
        
        for (const button of buttons) {
            if (!this.isValidButton(button)) continue;
            
            const buttonRect = button.getBoundingClientRect();
            const distance = Math.sqrt(
                Math.pow(buttonRect.left - textareaRect.left, 2) + 
                Math.pow(buttonRect.top - textareaRect.bottom, 2)
            );
            
            if (distance < 200) { // Within 200px
                nearbyButtons.push({ button, distance });
            }
        }
        
        // Sort by distance and return buttons
        return nearbyButtons
            .sort((a, b) => a.distance - b.distance)
            .map(item => item.button);
    }
    
    async pastePrompt(textarea, promptText) {
        // Focus the textarea
        textarea.focus();
        await this.sleep(100);
        
        // Clear existing content
        textarea.select();
        await this.sleep(50);
        
        // Set the value
        textarea.value = promptText;
        
        // Trigger input events to ensure the page recognizes the change
        textarea.dispatchEvent(new Event('input', { bubbles: true }));
        textarea.dispatchEvent(new Event('change', { bubbles: true }));
        
        // For React/Vue apps, trigger additional events
        const inputEvent = new InputEvent('input', {
            bubbles: true,
            cancelable: true,
            inputType: 'insertText',
            data: promptText
        });
        textarea.dispatchEvent(inputEvent);
        
        await this.sleep(200);
    }
    
    async clickGenerateButton(button) {
        // Scroll button into view
        button.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await this.sleep(300);
        
        // Focus and click
        button.focus();
        await this.sleep(100);
        
        // Try multiple click methods to ensure compatibility
        button.click();
        
        // Also try dispatching mouse events
        const mouseEvents = ['mousedown', 'mouseup', 'click'];
        for (const eventType of mouseEvents) {
            const event = new MouseEvent(eventType, {
                bubbles: true,
                cancelable: true,
                view: window
            });
            button.dispatchEvent(event);
        }
        
        // Try keyboard activation as backup
        const keyboardEvent = new KeyboardEvent('keydown', {
            bubbles: true,
            cancelable: true,
            key: 'Enter',
            code: 'Enter'
        });
        button.dispatchEvent(keyboardEvent);
        
        await this.sleep(500);
    }
    
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // Utility method to check if we're on a supported page
    isSupportedPage() {
        const url = window.location.href;
        const supportedDomains = [
            'runware.ai',
            'playground',
            'openai.com',
            'chat.openai.com',
            'claude.ai',
            'bard.google.com'
        ];
        
        return supportedDomains.some(domain => url.includes(domain));
    }
}

// Initialize the content script
const interactor = new WebpageInteractor();

// Add keyboard shortcut support (Ctrl+Enter to trigger from page)
document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.key === 'Enter') {
        // This could be used for additional functionality if needed
        console.log('Ctrl+Enter detected on page');
    }
});

console.log('Prompt Processor content script initialized');
