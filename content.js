class WebpageInteractor {
    constructor() {
        this.isProcessing = false;
        this.selectors = {
            // Specific selectors for Runware.ai and common AI interfaces
            textarea: [
                'textarea[placeholder="Type your prompt here to start..."]',
                'textarea[placeholder*="Type your prompt here to start"]',
                'textarea[placeholder*="Type your prompt"]',
                'textarea[placeholder*="Enter your"]',
                'textarea.w-full.bg-transparent',
                'textarea.scrollable-content',
                'textarea.w-full',
                'textarea[name="prompt"]',
                'textarea[id*="prompt"]',
                '#promptInput',
                'textarea'
            ],
            generateButton: [
                'button[id*="submit-btn"]',
                'button.MuiButton-root:has(span:contains("Generate"))',
                'button:has(span:contains("Generate"))',
                'button[aria-label*="Generate"]',
                'button:contains("Generate")',
                'button[type="submit"]',
                'button:contains("Send")',
                'button:contains("Submit")',
                'input[type="submit"]',
                'button.btn-primary',
                'button.generate-btn',
                '[data-testid="send-button"]',
                'button[class*="btn"]'
            ]
        };

        this.init();
    }
    
    init() {
        // Listen for messages from popup
        browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.type === 'processPrompt') {
                this.processPrompt(message.prompt, message.autoScroll)
                    .then(result => sendResponse(result))
                    .catch(error => sendResponse({ success: false, error: error.message }));
                return true; // Keep message channel open for async response
            }
        });
        
        console.log('Prompt Processor content script loaded');
    }
    
    async processPrompt(promptText, autoScroll = true) {
        try {
            console.log('🚀 Starting to process prompt:', promptText);
            console.log('📍 Current URL:', window.location.href);

            // Find textarea
            console.log('🔍 Looking for textarea...');
            const textarea = this.findTextarea();
            if (!textarea) {
                console.error('❌ No textarea found');
                this.debugPageElements();
                throw new Error('Could not find textarea on this page');
            }
            console.log('✅ Found textarea:', textarea);

            // Scroll to textarea if enabled
            if (autoScroll) {
                console.log('📜 Scrolling to textarea...');
                textarea.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await this.sleep(500);
            }

            // Clear existing content and paste new prompt
            console.log('📝 Pasting prompt into textarea...');
            await this.pastePrompt(textarea, promptText);
            console.log('✅ Prompt pasted successfully');

            // Find generate button (including disabled ones)
            console.log('🔍 Looking for generate button...');
            let generateButton = this.findGenerateButton(true); // Include disabled buttons
            if (!generateButton) {
                console.error('❌ No generate button found');
                this.debugPageElements();
                throw new Error('Could not find generate button on this page');
            }
            console.log('✅ Found generate button:', generateButton);

            // Check if button is disabled and wait for it to become enabled
            if (generateButton.disabled || generateButton.hasAttribute('disabled')) {
                console.log('⚠️ Button is disabled, waiting for it to become enabled...');
                const enabled = await this.waitForButtonToEnable(generateButton, 5000);
                if (!enabled) {
                    throw new Error('Generate button remained disabled after pasting prompt');
                }
            }

            // Click the generate button
            console.log('🖱️ Clicking generate button...');
            await this.clickGenerateButton(generateButton);
            console.log('✅ Generate button clicked successfully');

            // Send success message back to popup
            browser.runtime.sendMessage({
                type: 'promptProcessed',
                success: true
            });

            return { success: true };

        } catch (error) {
            console.error('❌ Error processing prompt:', error);

            // Send error message back to popup
            browser.runtime.sendMessage({
                type: 'promptProcessed',
                success: false,
                error: error.message
            });

            return { success: false, error: error.message };
        }
    }
    
    findTextarea() {
        console.log('🔍 Searching for textarea with selectors:', this.selectors.textarea);

        // Try each selector until we find a textarea
        for (const selector of this.selectors.textarea) {
            try {
                const elements = document.querySelectorAll(selector);
                console.log(`Selector "${selector}" found ${elements.length} elements`);

                for (const element of elements) {
                    if (this.isValidTextarea(element)) {
                        console.log('✅ Found valid textarea with selector:', selector, element);
                        return element;
                    } else {
                        console.log('❌ Invalid textarea:', element, this.getElementInfo(element));
                    }
                }
            } catch (error) {
                console.log('❌ Error with selector:', selector, error);
            }
        }

        // Fallback: find any visible textarea
        console.log('🔄 Trying fallback: searching all textareas');
        const allTextareas = document.querySelectorAll('textarea');
        console.log(`Found ${allTextareas.length} total textareas on page`);

        for (const textarea of allTextareas) {
            console.log('Checking textarea:', this.getElementInfo(textarea));
            if (this.isValidTextarea(textarea)) {
                console.log('✅ Found valid textarea via fallback:', textarea);
                return textarea;
            }
        }

        console.log('❌ No valid textarea found');
        return null;
    }
    
    isValidTextarea(element) {
        if (!element || element.tagName !== 'TEXTAREA') return false;
        
        const style = window.getComputedStyle(element);
        const rect = element.getBoundingClientRect();
        
        // Check if element is visible and has reasonable size
        return style.display !== 'none' && 
               style.visibility !== 'hidden' && 
               rect.width > 50 && 
               rect.height > 20 &&
               !element.disabled &&
               !element.readOnly;
    }
    
    findGenerateButton(includeDisabled = false) {
        console.log('🔍 Searching for generate button...');

        // Try text-based search first
        const buttons = document.querySelectorAll('button, input[type="submit"], input[type="button"]');
        console.log(`Found ${buttons.length} total buttons on page`);

        for (const button of buttons) {
            const text = button.textContent?.toLowerCase() || button.value?.toLowerCase() || '';
            const ariaLabel = button.getAttribute('aria-label')?.toLowerCase() || '';
            const id = button.id?.toLowerCase() || '';

            console.log('Checking button:', {
                text: text,
                ariaLabel: ariaLabel,
                id: id,
                element: button,
                info: this.getElementInfo(button)
            });

            if (text.includes('generate') ||
                text.includes('send') ||
                text.includes('submit') ||
                ariaLabel.includes('generate') ||
                ariaLabel.includes('send') ||
                id.includes('submit')) {

                if (this.isValidButton(button, includeDisabled)) {
                    console.log('✅ Found generate button by text:', button);
                    return button;
                }
            }
        }

        // Try selector-based search
        console.log('🔄 Trying selector-based search...');
        for (const selector of this.selectors.generateButton) {
            try {
                const element = document.querySelector(selector);
                if (element && this.isValidButton(element, includeDisabled)) {
                    console.log('✅ Found generate button with selector:', selector, element);
                    return element;
                }
            } catch (e) {
                console.log('❌ Error with selector:', selector, e);
            }
        }

        // Fallback: look for primary buttons near textarea
        console.log('🔄 Trying fallback: looking for nearby buttons...');
        const textarea = this.findTextarea();
        if (textarea) {
            const nearbyButtons = this.findNearbyButtons(textarea, includeDisabled);
            console.log(`Found ${nearbyButtons.length} nearby buttons`);
            if (nearbyButtons.length > 0) {
                console.log('✅ Found nearby button as fallback:', nearbyButtons[0]);
                return nearbyButtons[0];
            }
        }

        console.log('❌ No generate button found');
        return null;
    }
    
    isValidButton(element, includeDisabled = false) {
        if (!element) return false;

        const style = window.getComputedStyle(element);
        const rect = element.getBoundingClientRect();

        const isVisible = style.display !== 'none' &&
                         style.visibility !== 'hidden' &&
                         rect.width > 10 &&
                         rect.height > 10;

        if (!isVisible) return false;

        // If we're including disabled buttons, return true even if disabled
        if (includeDisabled) return true;

        // Otherwise, button must not be disabled
        return !element.disabled && !element.hasAttribute('disabled');
    }
    
    findNearbyButtons(textarea, includeDisabled = false) {
        const textareaRect = textarea.getBoundingClientRect();
        const buttons = document.querySelectorAll('button, input[type="submit"]');
        const nearbyButtons = [];

        for (const button of buttons) {
            if (!this.isValidButton(button, includeDisabled)) continue;

            const buttonRect = button.getBoundingClientRect();
            const distance = Math.sqrt(
                Math.pow(buttonRect.left - textareaRect.left, 2) +
                Math.pow(buttonRect.top - textareaRect.bottom, 2)
            );

            if (distance < 200) { // Within 200px
                nearbyButtons.push({ button, distance });
            }
        }

        // Sort by distance and return buttons
        return nearbyButtons
            .sort((a, b) => a.distance - b.distance)
            .map(item => item.button);
    }

    async waitForButtonToEnable(button, maxWaitTime = 10000) {
        console.log('⏳ Waiting for button to become enabled...');
        const startTime = Date.now();

        while (Date.now() - startTime < maxWaitTime) {
            if (!button.disabled && !button.hasAttribute('disabled')) {
                console.log('✅ Button is now enabled!');
                return true;
            }
            await this.sleep(100);
        }

        console.log('⏰ Timeout waiting for button to enable');
        return false;
    }
    
    async pastePrompt(textarea, promptText) {
        console.log('📝 Starting to paste prompt:', promptText);
        console.log('📝 Target textarea:', this.getElementInfo(textarea));

        // Focus the textarea
        console.log('🎯 Focusing textarea...');
        textarea.focus();
        await this.sleep(200);

        // Clear existing content
        console.log('🧹 Clearing existing content...');
        textarea.select();
        await this.sleep(100);

        // Try multiple methods to set the value
        console.log('✏️ Setting textarea value...');

        // Method 1: Clear and set value directly
        textarea.value = '';
        textarea.value = promptText;

        // Method 2: Use setRangeText for better compatibility
        textarea.setSelectionRange(0, textarea.value.length);
        textarea.setRangeText(promptText);

        // Method 3: Use document.execCommand (deprecated but still works)
        try {
            textarea.select();
            document.execCommand('insertText', false, promptText);
        } catch (e) {
            console.log('execCommand failed:', e);
        }

        console.log('📝 Textarea value after setting:', textarea.value);

        // Trigger comprehensive events to ensure the page recognizes the change
        console.log('🔔 Triggering events...');

        // Basic events
        textarea.dispatchEvent(new Event('focus', { bubbles: true }));
        textarea.dispatchEvent(new Event('input', { bubbles: true }));
        textarea.dispatchEvent(new Event('change', { bubbles: true }));
        textarea.dispatchEvent(new Event('blur', { bubbles: true }));

        // Modern input event
        const inputEvent = new InputEvent('input', {
            bubbles: true,
            cancelable: true,
            inputType: 'insertText',
            data: promptText
        });
        textarea.dispatchEvent(inputEvent);

        // Keyboard events
        textarea.dispatchEvent(new KeyboardEvent('keydown', { bubbles: true }));
        textarea.dispatchEvent(new KeyboardEvent('keyup', { bubbles: true }));

        // React/Vue specific events
        const reactEvent = new Event('input', { bubbles: true });
        reactEvent.simulated = true;
        textarea.dispatchEvent(reactEvent);

        await this.sleep(300);
        console.log('✅ Prompt pasting completed');
    }
    
    async clickGenerateButton(button) {
        // Scroll button into view
        button.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await this.sleep(300);
        
        // Focus and click
        button.focus();
        await this.sleep(100);
        
        // Try multiple click methods to ensure compatibility
        button.click();
        
        // Also try dispatching mouse events
        const mouseEvents = ['mousedown', 'mouseup', 'click'];
        for (const eventType of mouseEvents) {
            const event = new MouseEvent(eventType, {
                bubbles: true,
                cancelable: true,
                view: window
            });
            button.dispatchEvent(event);
        }
        
        // Try keyboard activation as backup
        const keyboardEvent = new KeyboardEvent('keydown', {
            bubbles: true,
            cancelable: true,
            key: 'Enter',
            code: 'Enter'
        });
        button.dispatchEvent(keyboardEvent);
        
        await this.sleep(500);
    }
    
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Debug helper methods
    getElementInfo(element) {
        if (!element) return 'null';

        const rect = element.getBoundingClientRect();
        const style = window.getComputedStyle(element);

        return {
            tagName: element.tagName,
            id: element.id,
            className: element.className,
            placeholder: element.placeholder,
            textContent: element.textContent?.substring(0, 50),
            value: element.value?.substring(0, 50),
            disabled: element.disabled,
            readOnly: element.readOnly,
            visible: style.display !== 'none' && style.visibility !== 'hidden',
            dimensions: `${rect.width}x${rect.height}`,
            position: `${rect.left},${rect.top}`
        };
    }

    debugPageElements() {
        console.log('🔍 DEBUG: Page elements analysis');
        console.log('URL:', window.location.href);

        // Debug textareas
        const allTextareas = document.querySelectorAll('textarea');
        console.log(`Found ${allTextareas.length} textareas:`);
        allTextareas.forEach((textarea, index) => {
            console.log(`Textarea ${index + 1}:`, this.getElementInfo(textarea));
        });

        // Debug buttons
        const allButtons = document.querySelectorAll('button, input[type="submit"], input[type="button"]');
        console.log(`Found ${allButtons.length} buttons:`);
        allButtons.forEach((button, index) => {
            console.log(`Button ${index + 1}:`, this.getElementInfo(button));
        });
    }

    // Utility method to check if we're on a supported page
    isSupportedPage() {
        const url = window.location.href;
        const supportedDomains = [
            'runware.ai',
            'playground',
            'openai.com',
            'chat.openai.com',
            'claude.ai',
            'bard.google.com'
        ];

        return supportedDomains.some(domain => url.includes(domain));
    }
}

// Initialize the content script
const interactor = new WebpageInteractor();

// Add keyboard shortcut support (Ctrl+Enter to trigger from page)
document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.key === 'Enter') {
        // This could be used for additional functionality if needed
        console.log('Ctrl+Enter detected on page');
    }
});

// Add manual test function for debugging
window.testPromptProcessor = function(testPrompt = "Test prompt from console") {
    console.log('🧪 Manual test started...');
    interactor.processPrompt(testPrompt, true)
        .then(result => {
            console.log('🧪 Manual test result:', result);
        })
        .catch(error => {
            console.error('🧪 Manual test error:', error);
        });
};

// Add debug function
window.debugPromptProcessor = function() {
    console.log('🔍 Debug information:');
    interactor.debugPageElements();

    console.log('🔍 Testing textarea detection:');
    const textarea = interactor.findTextarea();
    console.log('Found textarea:', textarea);

    console.log('🔍 Testing button detection:');
    const button = interactor.findGenerateButton();
    console.log('Found button:', button);
};

console.log('Prompt Processor content script initialized');
console.log('💡 Debug commands available:');
console.log('  - testPromptProcessor("your test prompt")');
console.log('  - debugPromptProcessor()');
console.log('  - interactor.debugPageElements()');
