# Icon Instructions

You need to create the following icon files for the extension to work properly:

## Required Icon Files:

1. **icon-16.png** (16x16 pixels) - Toolbar icon small
2. **icon-32.png** (32x32 pixels) - Toolbar icon medium  
3. **icon-48.png** (48x48 pixels) - Extension management page
4. **icon-128.png** (128x128 pixels) - Chrome Web Store (if publishing)

## Optional Disabled State Icons:
1. **icon-16-disabled.png** (16x16 pixels) - Grayed out version
2. **icon-32-disabled.png** (32x32 pixels) - Grayed out version
3. **icon-48-disabled.png** (48x48 pixels) - Grayed out version
4. **icon-128-disabled.png** (128x128 pixels) - Grayed out version

## Design Suggestions:

- Use a simple, recognizable symbol like:
  - A text cursor with arrows (representing automated input)
  - A gear/cog with text lines
  - A robot head with speech bubble
  - Stacked horizontal lines with a play button
  
- Colors: Use blue (#007bff) as primary color to match the extension theme
- Background: Transparent or white
- Style: Modern, flat design with clear visibility at small sizes

## Quick Creation Options:

1. **Online Icon Generators**: Use tools like Canva, Figma, or online icon generators
2. **AI Image Generators**: Use DALL-E, Midjourney, or similar tools with prompt: "Simple flat icon for browser extension, blue color, representing automated text processing"
3. **Icon Libraries**: Download from sites like Flaticon, Icons8, or Feather Icons and modify colors
4. **Simple Text Icons**: Create simple text-based icons with letters "PP" (Prompt Processor)

## Temporary Solution:

For testing purposes, you can:
1. Create simple colored squares in any image editor
2. Use any existing small PNG files and rename them
3. The extension will work without icons, but Firefox may show warnings

## File Format:
- Format: PNG with transparency
- Color depth: 24-bit or 32-bit (with alpha channel)
- Compression: Standard PNG compression
