// Background script for Prompt Processor extension

class BackgroundManager {
    constructor() {
        this.init();
    }
    
    init() {
        // Handle extension installation
        browser.runtime.onInstalled.addListener((details) => {
            if (details.reason === 'install') {
                console.log('Prompt Processor extension installed');
                this.showWelcomeNotification();
            } else if (details.reason === 'update') {
                console.log('Prompt Processor extension updated');
            }
        });
        
        // Handle messages between popup and content scripts
        browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
            console.log('Background received message:', message);
            
            switch (message.type) {
                case 'getActiveTab':
                    this.getActiveTab().then(sendResponse);
                    return true;
                    
                case 'checkTabCompatibility':
                    this.checkTabCompatibility(message.tabId).then(sendResponse);
                    return true;
                    
                case 'promptProcessed':
                    // Forward message to popup if it's open
                    this.forwardToPopup(message);
                    break;

                case 'ping':
                    console.log('🏓 Ping received from content script');
                    sendResponse({ success: true, message: 'Background script is working' });
                    return true;

                default:
                    console.log('Unknown message type:', message.type);
            }
        });
        
        // Handle tab updates to check compatibility
        browser.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && tab.url) {
                this.updateBrowserActionState(tab);
            }
        });
        
        // Handle tab activation
        browser.tabs.onActivated.addListener((activeInfo) => {
            browser.tabs.get(activeInfo.tabId).then(tab => {
                this.updateBrowserActionState(tab);
            });
        });
        
        console.log('Prompt Processor background script initialized');
    }
    
    async getActiveTab() {
        try {
            const tabs = await browser.tabs.query({ active: true, currentWindow: true });
            return tabs.length > 0 ? tabs[0] : null;
        } catch (error) {
            console.error('Error getting active tab:', error);
            return null;
        }
    }
    
    async checkTabCompatibility(tabId) {
        try {
            const tab = await browser.tabs.get(tabId);
            const isCompatible = this.isCompatibleUrl(tab.url);
            
            return {
                compatible: isCompatible,
                url: tab.url,
                title: tab.title
            };
        } catch (error) {
            console.error('Error checking tab compatibility:', error);
            return { compatible: false, error: error.message };
        }
    }
    
    isCompatibleUrl(url) {
        if (!url) return false;
        
        const compatiblePatterns = [
            /runware\.ai/i,
            /playground/i,
            /openai\.com/i,
            /chat\.openai\.com/i,
            /claude\.ai/i,
            /bard\.google\.com/i,
            /huggingface\.co/i,
            /replicate\.com/i,
            /cohere\.ai/i,
            /anthropic\.com/i
        ];
        
        return compatiblePatterns.some(pattern => pattern.test(url));
    }
    
    async updateBrowserActionState(tab) {
        try {
            const isCompatible = this.isCompatibleUrl(tab.url);
            
            // Update browser action icon and title based on compatibility
            if (isCompatible) {
                await browser.browserAction.setIcon({
                    tabId: tab.id,
                    path: {
                        "16": "icons/icon-16.png",
                        "32": "icons/icon-32.png",
                        "48": "icons/icon-48.png",
                        "128": "icons/icon-128.png"
                    }
                });
                await browser.browserAction.setTitle({
                    tabId: tab.id,
                    title: "Prompt Processor - Ready to process prompts"
                });
            } else {
                await browser.browserAction.setIcon({
                    tabId: tab.id,
                    path: {
                        "16": "icons/icon-16-disabled.png",
                        "32": "icons/icon-32-disabled.png",
                        "48": "icons/icon-48-disabled.png",
                        "128": "icons/icon-128-disabled.png"
                    }
                });
                await browser.browserAction.setTitle({
                    tabId: tab.id,
                    title: "Prompt Processor - Not compatible with this page"
                });
            }
        } catch (error) {
            console.error('Error updating browser action state:', error);
        }
    }
    
    async forwardToPopup(message) {
        try {
            // Try to send message to popup
            await browser.runtime.sendMessage(message);
        } catch (error) {
            // Popup might not be open, which is fine
            console.log('Could not forward message to popup (popup might be closed)');
        }
    }
    
    showWelcomeNotification() {
        // Create a welcome notification
        if (browser.notifications) {
            browser.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon-48.png',
                title: 'Prompt Processor Installed!',
                message: 'Click the extension icon on AI websites to start processing prompts automatically.'
            });
        }
    }
    
    // Utility method to inject content script if needed
    async injectContentScript(tabId) {
        try {
            await browser.tabs.executeScript(tabId, {
                file: 'content.js'
            });
            console.log('Content script injected successfully');
        } catch (error) {
            console.error('Error injecting content script:', error);
            throw error;
        }
    }
    
    // Handle context menu (optional feature)
    setupContextMenu() {
        browser.contextMenus.create({
            id: 'processSelectedText',
            title: 'Process selected text as prompt',
            contexts: ['selection']
        });
        
        browser.contextMenus.onClicked.addListener((info, tab) => {
            if (info.menuItemId === 'processSelectedText' && info.selectionText) {
                // Send selected text to content script for processing
                browser.tabs.sendMessage(tab.id, {
                    type: 'processPrompt',
                    prompt: info.selectionText,
                    autoScroll: true
                });
            }
        });
    }
}

// Initialize background manager
const backgroundManager = new BackgroundManager();

// Optional: Set up context menu
// backgroundManager.setupContextMenu();
