# Firefox Extension Installation Guide

## Quick Installation Steps

### 1. Prepare the Extension
- Ensure all files are in the extension directory
- Create icon files (see `icons/ICON_INSTRUCTIONS.md`) or use placeholder icons

### 2. Load in Firefox Developer Mode

1. **Open Firefox** and navigate to `about:debugging`
2. **Click "This Firefox"** in the left sidebar
3. **Click "Load Temporary Add-on..."**
4. **Navigate to your extension folder** and select `manifest.json`
5. **The extension will load** and appear in your toolbar

### 3. Test the Extension

1. **Visit a supported website** like:
   - runware.ai (as shown in your screenshot)
   - OpenAI Playground
   - Claude.ai
   - Any site with textarea and generate button

2. **Click the extension icon** in the Firefox toolbar
3. **Add some test prompts** in the popup
4. **Click "Start Processing"** to test functionality

## Troubleshooting Installation

### Extension not loading?
- Check that `manifest.json` is valid JSON
- Ensure all referenced files exist
- Look for error messages in the debugging console

### Icon warnings?
- Create the required icon files (see `icons/ICON_INSTRUCTIONS.md`)
- Or temporarily ignore - extension will work without icons

### Permission errors?
- Make sure you're on a supported website
- Check that the website URL matches the patterns in `manifest.json`

## Making it Permanent

### For Personal Use:
- The temporary installation method is sufficient
- Extension will need to be reloaded after Firefox restarts

### For Distribution:
1. **Create proper icons** (required for submission)
2. **Package as .xpi file**:
   ```bash
   zip -r prompt-processor.xpi * -x "*.git*" "*.md" "INSTALLATION.md"
   ```
3. **Submit to Firefox Add-ons** (optional)

## File Checklist

Before installation, ensure these files exist:
- ✅ `manifest.json`
- ✅ `popup.html`
- ✅ `popup.css`
- ✅ `popup.js`
- ✅ `content.js`
- ✅ `background.js`
- ⚠️ `icons/icon-16.png` (create or use placeholder)
- ⚠️ `icons/icon-32.png` (create or use placeholder)
- ⚠️ `icons/icon-48.png` (create or use placeholder)
- ⚠️ `icons/icon-128.png` (create or use placeholder)

## Next Steps After Installation

1. **Test on your target website** (runware.ai)
2. **Add multiple prompts** to test batch processing
3. **Adjust delay settings** as needed
4. **Report any issues** and modify code accordingly

## Development Mode

For development and testing:
- Keep the `about:debugging` page open
- Use "Reload" button to update after code changes
- Check browser console for error messages
- Use Firefox Developer Tools for debugging
